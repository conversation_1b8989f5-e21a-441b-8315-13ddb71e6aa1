import React from "react";
import type { ProgramsProps } from "../../types";

const Programs: React.FC<ProgramsProps> = ({ programs }) => {
  return (
    <section id="programs" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-semibold text-slate-900 mb-4 tracking-tight">
            Programs Offered
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Comprehensive health sciences education programs designed to meet
            industry standards and prepare students for successful careers.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {programs.map((program, index) => (
            <div
              key={index}
              className="bg-white border border-slate-200 rounded-2xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden group"
            >
              <div className="bg-gradient-to-br from-slate-800 to-slate-700 p-6">
                <h3 className="text-lg font-semibold text-white leading-tight">
                  {program.title}
                </h3>
              </div>
              <div className="p-6 space-y-6">
                <div>
                  <h4 className="font-semibold text-slate-900 mb-2 text-sm uppercase tracking-wider">
                    Duration
                  </h4>
                  <p className="text-slate-700">{program.duration}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-slate-900 mb-2 text-sm uppercase tracking-wider">
                    Entry Qualifications
                  </h4>
                  <p className="text-slate-700">{program.qualifications}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-slate-900 mb-2 text-sm uppercase tracking-wider">
                    Fee Structure
                  </h4>
                  <p className="text-slate-700">{program.fees}</p>
                </div>
                <button className="w-full bg-teal-600 hover:bg-teal-700 text-white py-3 px-4 rounded-xl font-semibold transition-all duration-300 shadow-sm hover:shadow-md">
                  Learn More
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Programs;
