import React, { useState, useEffect } from "react";
import {
  Navigation,
  Hero,
  Announcements,
  About,
  Programs,
  Gallery,
  Footer,
} from "./components";
import { slides, announcements, programs, galleryImages } from "./data";

function App() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [currentAnnouncement, setCurrentAnnouncement] = useState(0);

  // Auto-scroll announcements
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentAnnouncement((prev) => (prev + 1) % announcements.length);
    }, 3000);
    return () => clearInterval(timer);
  }, []);

  // Smooth scroll function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
    setIsMenuOpen(false);
  };

  return (
    <div className="min-h-screen bg-white">
      <Navigation
        isMenuOpen={isMenuOpen}
        setIsMenuOpen={setIsMenuOpen}
        scrollToSection={scrollToSection}
      />

      <Hero
        slides={slides}
        currentSlide={currentSlide}
        setCurrentSlide={setCurrentSlide}
        scrollToSection={scrollToSection}
      />

      <Announcements
        announcements={announcements}
        currentAnnouncement={currentAnnouncement}
      />

      <About />

      <Programs programs={programs} />

      <Gallery images={galleryImages} />

      <Footer scrollToSection={scrollToSection} />
    </div>
  );
}

export default App;
