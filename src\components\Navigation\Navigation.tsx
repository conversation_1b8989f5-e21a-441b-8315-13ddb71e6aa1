import React from "react";
import type { NavigationProps } from "../../types";
import logo from "../../assets/mkolani-logo.jpg";

const Navigation: React.FC<NavigationProps> = ({
  isMenuOpen,
  setIsMenuOpen,
  scrollToSection,
}) => {
  return (
    <nav className="bg-white/95 backdrop-blur-sm border-b border-slate-200 fixed w-full top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="md:flex-row flex-col justify-between items-center h-20">
          {/* Logo and Institution Name */}
          <div className="flex w-full items-center space-x-4">
            <div className="flex-shrink-0">
              <img
                src={logo}
                alt="MKOLANI Logo"
                className="h-12 w-12 rounded-full ring-2 ring-slate-100"
              />
            </div>
            <div className="hidden md:block">
              <h1 className="text-lg font-semibold text-slate-900 leading-tight tracking-tight">
                MKOLANI FOUNDATION HEALTH
                <br />
                SCIENCES TRAINING INSTITUTE
              </h1>
            </div>
            <div className="flex-shrink-0">
              <img
                src={logo}
                alt="MKOLANI Logo"
                className="h-12 w-12 rounded-full ring-2 ring-slate-100"
              />
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-1">
              <button
                onClick={() => scrollToSection("home")}
                className="text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-2 text-sm font-medium transition-all duration-200 rounded-lg"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection("about")}
                className="text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-2 text-sm font-medium transition-all duration-200 rounded-lg"
              >
                About
              </button>
              <button
                onClick={() => scrollToSection("programs")}
                className="text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-2 text-sm font-medium transition-all duration-200 rounded-lg"
              >
                Programs
              </button>
              <button
                onClick={() => scrollToSection("gallery")}
                className="text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-2 text-sm font-medium transition-all duration-200 rounded-lg"
              >
                Gallery
              </button>
              <button
                onClick={() => scrollToSection("contact")}
                className="text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-2 text-sm font-medium transition-all duration-200 rounded-lg"
              >
                Contact
              </button>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-slate-700 hover:text-slate-900 hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 p-2 rounded-lg transition-all duration-200"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-4 pt-2 pb-4 space-y-2 bg-white/95 backdrop-blur-sm border-t border-slate-200">
              <button
                onClick={() => scrollToSection("home")}
                className="block text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-3 text-base font-medium w-full text-left rounded-lg transition-all duration-200"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection("about")}
                className="block text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-3 text-base font-medium w-full text-left rounded-lg transition-all duration-200"
              >
                About
              </button>
              <button
                onClick={() => scrollToSection("programs")}
                className="block text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-3 text-base font-medium w-full text-left rounded-lg transition-all duration-200"
              >
                Programs
              </button>
              <button
                onClick={() => scrollToSection("gallery")}
                className="block text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-3 text-base font-medium w-full text-left rounded-lg transition-all duration-200"
              >
                Gallery
              </button>
              <button
                onClick={() => scrollToSection("contact")}
                className="block text-slate-700 hover:text-slate-900 hover:bg-slate-50 px-4 py-3 text-base font-medium w-full text-left rounded-lg transition-all duration-200"
              >
                Contact
              </button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
